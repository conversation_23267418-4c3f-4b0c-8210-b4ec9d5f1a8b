#!/usr/bin/env python3
"""
Script to remove the 'weight' field from frontmatter in .smd files
"""

import os
import re
import glob

def fix_weight_field(file_path):
    """Remove the weight field from a .smd file's frontmatter"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match the weight field line
    weight_pattern = r'^\s*\.weight\s*=\s*\d+,?\s*$'
    
    # Split content into lines
    lines = content.split('\n')
    
    # Remove weight field lines
    filtered_lines = []
    for line in lines:
        if not re.match(weight_pattern, line):
            filtered_lines.append(line)
    
    # Join lines back
    new_content = '\n'.join(filtered_lines)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Fixed: {file_path}")

def main():
    # Find all .smd files in content/learn directory
    learn_files = glob.glob('content/learn/*.smd')
    
    for file_path in learn_files:
        if os.path.isfile(file_path):
            fix_weight_field(file_path)
    
    print(f"Processed {len(learn_files)} files")

if __name__ == '__main__':
    main()
