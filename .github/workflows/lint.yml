name: Lint

on:
  workflow_dispatch:
  pull_request:
    paths:
      - "**.md"
      - "**.org"
      - ".github/workflows/**"
  push:
    branches:
      - main
    paths:
      - "**.md"
      - "**.org"
      - ".github/workflows/**"

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v4
        with:
          node-version: "latest"
      - name: Prettier check
        run: |
          echo "If lint fails, run `make format` and commit again."
          make lint
