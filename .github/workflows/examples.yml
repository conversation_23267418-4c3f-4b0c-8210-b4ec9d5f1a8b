name: Run Examples

on:
  schedule:
    - cron: "10 20 * * *"
  pull_request:
    paths:
      - "examples/**"
      - ".github/workflows/examples.yml"
  push:
    branches:
      - main
    paths:
      - "examples/**"
      - ".github/workflows/examples.yml"

defaults:
  run:
    working-directory: examples

jobs:
  examples:
    timeout-minutes: 10
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]
        zig: [0.12.0]
    steps:
      - uses: actions/checkout@v4

      - uses: goto-bus-stop/setup-zig@v2
        with:
          version: ${{ matrix.zig }}

      - name: Run Examples
        run: |
          ./run-all.sh
