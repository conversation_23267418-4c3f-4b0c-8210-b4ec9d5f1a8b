#!/usr/bin/env python3
"""
Script to fix unknown language codes in .smd files
"""

import os
import re
import glob

def fix_language_codes(file_path):
    """Fix language codes in a .smd file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Language code mappings
    replacements = {
        '```plain': '```text',
        '```Zig': '```zig',
        '```C++': '```cpp',
        '```shell': '```bash',
        '```mermaid': '```text',  # Zine doesn't support mermaid, convert to text
    }
    
    # Apply replacements
    modified = False
    for old, new in replacements.items():
        if old in content:
            content = content.replace(old, new)
            modified = True
    
    # Write back to file if modified
    if modified:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed: {file_path}")
        return True
    return False

def main():
    # Find all .smd files
    smd_files = []
    for root, dirs, files in os.walk('content'):
        for file in files:
            if file.endswith('.smd'):
                smd_files.append(os.path.join(root, file))
    
    fixed_count = 0
    for file_path in smd_files:
        if fix_language_codes(file_path):
            fixed_count += 1
    
    print(f"Fixed {fixed_count} files out of {len(smd_files)} total files")

if __name__ == '__main__':
    main()
