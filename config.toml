baseURL = 'https://ziglang.cc'
languageCode = 'en'
defaultContentLanguage = 'zh-cn'
title = 'Zig 语言中文社区'
description = 'Zig Chinese Community is dedicated to sharing and spreading the use of Zig language among Chinese users.'
copyright = """ © 2022–2025
  | <a href="https://github.com/zigcc/zigcc.github.io">Source Code</a>
  | <a href="https://creativecommons.org/licenses/by-nc-nd/4.0/">BY-NC-ND 4.0</a>
  | <a href="/index.xml">RSS</a>
"""

[[menu.main]]
  name = "学习 Zig"
  weight = 11
  url = "/learn"
[[menu.main]]
  name = "月刊"
  weight = 20
  url = "/monthly"
[[menu.main]]
  name = "博客"
  weight = 21
  url = "/post"
[[menu.main]]
  name = "贡献"
  weight = 30
  url = "/contributing"
[[menu.main]]
  name = "社区"
  weight = 40
  url = "/community"

[params]
  github_repo = "https://github.com/zigcc/zigcc.github.io"
  offlineSearch = true
  time_format_blog = "2006-01-02"
  [params.giscus]
    repo = "zigcc/zigcc.github.io"
    repoId = "R_kgDOHr7nSg"
    category = "Announcements"
    categoryId = "DIC_kwDOHr7nSs4CQUsK"

[params.ui]
# navbar_logo = false
showLightDarkModeMenu = true

[params.links]
[[params.links.user]]
  name = "GitHub Discussion"
  url = "https://github.com/orgs/zigcc/discussions"
  icon = "fab fa-github"
  desc = "新手提问；经验分享"
[[params.links.user]]
  name = "Discord"
  url = "https://discord.gg/UraRxD6WXD"
  icon = "fab fa-discord"
[[params.links.user]]
  name = "Telegram"
  url = "https://t.me/ZigChinese"
  icon = "fab fa-telegram"
[[params.links.user]]
  name = "微信群"
  url = "https://github.com/orgs/zigcc/discussions/134"
  icon = "fab fa-weixin"
[[params.links.user]]
  name = "微信公众号"
  url = "https://github.com/zigcc/.github/blob/main/zig_mp.png"
  icon = "fab fa-weixin"
  desc = "跟进社区最新动态"
[[params.links.user]]
  name = "邮件"
  url = "mailto:<EMAIL>"
  icon = "fa fa-envelope"
  desc = ""

[[params.links.developer]]
  name = "文章供稿"
  url = "/post/2023/09/05/hello-world/"
  icon = "fab fa-blogger"
  desc = "分享 Zig 使用经验，改进网站体验"
[[params.links.developer]]
  name = "Zig 圣经"
  url = "https://course.ziglang.cc/"
  icon = "fa fa-book"
  desc = "完善、改进圣经内容"
[[params.links.developer]]
  name = "线上会议"
  url = "/post/news"
  icon = "fa-brands fa-meetup"
  desc = "我们会定期举行线上会议，畅聊 Zig 中的一切"

[module]
# proxy = "https://goproxy.cn,direct"
  [[module.imports]]
    path = "github.com/google/docsy"

[frontmatter]
  date = [":filename", ":default", ":fileModTime"]
  lastmod = ['lastmod', ':git', ':fileModTime', ':default']

[permalinks]
  post = "/post/:year/:month/:day/:slug/"

[markup]
  [markup.tableOfContents]
    startLevel = 1
    endLevel = 8
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true
    [markup.goldmark.parser.attribute]
      block = true
  # https://gohugo.io/getting-started/configuration-markup/#highlight
  [markup.highlight]
    style = "pygments"
    linenos = true

[outputs]
section = [ "HTML", "RSS", "print" ]
