[[https://github.com/zigcc/zigcc.github.io/actions/workflows/gh-pages.yml][https://github.com/zigcc/zigcc.github.io/actions/workflows/gh-pages.yml/badge.svg]]

* Zig 语言中文社区
- [[https://ziglang.cc]]
- 如果没有特殊声明，本网站内容均采用 [[https://creativecommons.org/licenses/by-nc-nd/4.0/][CC BY-NC-ND 4.0]] 协议

#+BEGIN_EXPORT html
<center>
<p>欢迎来到 Zig 语言中文社区！（Zig Chinese Community，简称：ZigCC）</p>
<a href="https://discord.gg/UraRxD6WXD">
<img src="https://img.shields.io/discord/1155469703846834187?label=Chat at Discord" />
</a>
<a href="/index.xml">
<img src="https://img.shields.io/badge/rss-F88900.svg?style=flat&logo=RSS&logoColor=white&label=网站" />
</a>
<a href="https://ask.ziglang.cc/atom.xml">
<img src="https://img.shields.io/badge/rss-F88900.svg?style=flat&logo=RSS&logoColor=white&label=论坛" />
</a>
</center>
#+END_EXPORT


#+begin_quote
Zig Chinese Community is dedicated to sharing and spreading the use of Zig language among Chinese users.
#+end_quote

本网站使用 [[https://gohugo.io/][hugo]]（extended 版本）与 [[https://www.docsy.dev/][docsy]] 主题进行构建。

#+begin_src bash
# For macOS
brew install hugo
# For Debian
sudo apt install hugo
# For Arch
sudo pacman -S hugo

# Snap
sudo snap install hugo
#+end_src

#+begin_src bash
# run server
hugo server
#+end_src

如果启动 server 时报错，可以安装以下依赖：
#+begin_src bash
# install depends
npm i -D postcss postcss-cli autoprefixer
#+end_src

参考：https://www.docsy.dev/docs/get-started/docsy-as-module/installation-prerequisites/#install-postcss
