#!/usr/bin/env python3
"""
Script to convert Hugo markdown files to zig-zine SuperMD format.
"""

import os
import re
import yaml
from pathlib import Path
from datetime import datetime

def convert_frontmatter(frontmatter_str):
    """Convert YAML frontmatter to Ziggy format."""
    try:
        data = yaml.safe_load(frontmatter_str)
    except yaml.YAMLError:
        return None
    
    ziggy_lines = ["---"]
    
    # Required fields
    if 'title' in data:
        ziggy_lines.append(f'.title = "{data["title"]}",')
    
    # Date handling
    if 'date' in data:
        date_str = str(data['date'])
        if 'T' in date_str:
            # ISO format
            ziggy_lines.append(f'.date = @date("{date_str}"),')
        else:
            # Try to parse and format
            try:
                dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                ziggy_lines.append(f'.date = @date("{dt.isoformat()}"),')
            except:
                ziggy_lines.append(f'.date = @date("2023-01-01T00:00:00"),')
    else:
        ziggy_lines.append('.date = @date("2023-01-01T00:00:00"),')
    
    # Author
    if 'author' in data:
        ziggy_lines.append(f'.author = "{data["author"]}",')
    else:
        ziggy_lines.append('.author = "ZigCC",')
    
    # Layout determination
    layout = "page.shtml"
    if 'type' in data and data['type'] == 'docs':
        layout = "section.shtml"
    elif '/post/' in str(data.get('path', '')):
        layout = "post.shtml"
    
    ziggy_lines.append(f'.layout = "{layout}",')
    ziggy_lines.append('.draft = false,')
    
    # Additional fields
    if 'weight' in data:
        ziggy_lines.append(f'.weight = {data["weight"]},')
    
    ziggy_lines.append("---")
    return '\n'.join(ziggy_lines)

def convert_content(content):
    """Convert Hugo-specific syntax to SuperMD."""
    # Remove Hugo shortcodes
    content = re.sub(r'\{\{%.*?%\}\}', '', content, flags=re.DOTALL)
    content = re.sub(r'\{\{<.*?>>\}\}', '', content, flags=re.DOTALL)
    
    # Convert Hugo ref links
    content = re.sub(r'\{\{< ref "(.*?)" >\}\}', r'/\1', content)
    
    # Clean up extra whitespace
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    return content.strip()

def convert_file(input_path, output_path):
    """Convert a single markdown file to SuperMD."""
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split frontmatter and content
        if content.startswith('---'):
            parts = content.split('---', 2)
            if len(parts) >= 3:
                frontmatter_str = parts[1]
                body = parts[2].strip()
            else:
                frontmatter_str = ""
                body = content
        else:
            frontmatter_str = ""
            body = content
        
        # Convert frontmatter
        ziggy_frontmatter = convert_frontmatter(frontmatter_str)
        if not ziggy_frontmatter:
            print(f"Warning: Could not convert frontmatter for {input_path}")
            return False
        
        # Convert content
        converted_body = convert_content(body)
        
        # Write output
        output_content = ziggy_frontmatter + '\n\n' + converted_body
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(output_content)
        
        print(f"Converted: {input_path} -> {output_path}")
        return True
        
    except Exception as e:
        print(f"Error converting {input_path}: {e}")
        return False

def main():
    """Main conversion function."""
    content_dir = Path("content")
    
    # Find all markdown files
    md_files = list(content_dir.glob("**/*.md"))
    org_files = list(content_dir.glob("**/*.org"))
    
    converted_count = 0
    total_count = len(md_files) + len(org_files)
    
    # Convert markdown files
    for md_file in md_files:
        # Skip if .smd already exists
        smd_file = md_file.with_suffix('.smd')
        if smd_file.exists():
            print(f"Skipping {md_file} (already converted)")
            continue
            
        if convert_file(md_file, smd_file):
            converted_count += 1
    
    # Convert org files (basic conversion)
    for org_file in org_files:
        smd_file = org_file.with_suffix('.smd')
        if smd_file.exists():
            print(f"Skipping {org_file} (already converted)")
            continue
            
        # Basic org to markdown conversion
        try:
            with open(org_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract title and date from org format
            title_match = re.search(r'#\+TITLE:\s*(.+)', content)
            date_match = re.search(r'#\+DATE:\s*(.+)', content)
            
            title = title_match.group(1) if title_match else org_file.stem
            date_str = date_match.group(1) if date_match else "2023-01-01T00:00:00"
            
            # Create frontmatter
            frontmatter = f"""---
.title = "{title}",
.date = @date("{date_str}"),
.author = "ZigCC",
.layout = "post.shtml",
.draft = false,
---"""
            
            # Remove org metadata
            body = re.sub(r'#\+\w+:.*\n', '', content)
            body = convert_content(body)
            
            output_content = frontmatter + '\n\n' + body
            
            with open(smd_file, 'w', encoding='utf-8') as f:
                f.write(output_content)
            
            print(f"Converted: {org_file} -> {smd_file}")
            converted_count += 1
            
        except Exception as e:
            print(f"Error converting {org_file}: {e}")
    
    print(f"\nConversion complete: {converted_count}/{total_count} files converted")

if __name__ == "__main__":
    main()
