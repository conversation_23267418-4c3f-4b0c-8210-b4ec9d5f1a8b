---
title: Learning Zig 中文翻译
type: docs
cascade:
  - type: docs
---

[《学习 Zig》](https://www.openmymind.net/learning_zig/)系列教程最初由 [<PERSON> Seguin](https://github.com/karlseguin) 编写，该教程行文流畅，讲述的脉络由浅入深，深入浅出，是入门 Zig 非常不错的选择。因此，[Zig 中文社区](https://ziglang.cc)将其翻译成中文，便于在中文用户内阅读与传播。

初次接触 Zig 的用户可以按序号依次阅读，对于有经验的 Zig 开发者可按需阅读感兴趣的章节。

## 关于原作者

[Karl Seguin](https://www.linkedin.com/in/karlseguin/) 在多个领域有着丰富经验，前微软 MVP，他撰写了大量文章，是多个微软公共新闻组的活跃成员。现居新加坡。他还是以下教程的作者：

- [The Little Go Book](https://github.com/karlseguin/the-little-go-book)
- [The Little Redis Book](https://github.com/karlseguin/the-little-redis-book)
- [The Little MongoDB Book](https://github.com/karlseguin/the-little-mongodb-book)

可以在 <http://openmymind.net> 找到他的博客，或者通过 [@karlseguin](http://twitter.com/karlseguin) 在 Twitter 上关注他。

## 翻译原则

技术文档的翻译首要原则是准确，但在准确的前提下如何保证『信、达、雅』？这是个挑战，在翻译本教程时，在某些情况下会根据上下文进行意译，便于中文读者阅读。

最后，感谢翻译者的无私贡献。❤️️

## 离线阅读

在本仓库的 [release 页面](https://github.com/zigcc/zigcc.github.io/releases)会定期将本教程导出为 PDF 格式，读者可按需下载。

读者也可以使用右侧导航栏中的『[整节打印](_print)』将当前版本教程保存为 PDF 格式。

## 其他学习资料

由于 Zig 目前还处于快速迭代，因此最权威的资料无疑是官方的 [Zig Language Reference](https://ziglang.org/documentation/master/)，遇到语言的细节问题，基本都可以在这里找到答案。 其次是社区的一些高质量教程，例如：

- **[Zig 日报](https://github.com/zigcc/forum/issues):**
- **[Zig 语言圣经](https://course.ziglang.cc):** 一份内容全面、深入浅出介绍 Zig 的教程
- **[ziglings/exercises](https://codeberg.org/ziglings/exercises/):** Learn the Zig programming language by fixing tiny broken programs.
- **[Zig Cookbook](https://cookbook.ziglang.cc/):** A collection of simple Zig programs that demonstrate good practices to accomplish common programming tasks
- **[Awesome Zig](https://github.com/zigcc/awesome-zig):** A collection of some awesome public Zig programming language projects.
- **[Zig Guide](https://zig.guide/):** 英文资料， [Sobeston](https://github.com/Sobeston) 编写
- **[Zig in 30 minutes](https://gist.github.com/ityonemo/769532c2017ed9143f3571e5ac104e50):**
