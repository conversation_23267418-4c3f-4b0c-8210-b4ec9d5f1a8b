#+TITLE: 学习 Zig
#+DATE: 2024-08-04T08:51:07+0800
#+LASTMOD: 2024-08-18T11:48:26+0800
{{% blocks/cover title="学习 Zig" image_anchor="bottom" height="auto" %}}


{{% /blocks/cover %}}

{{% blocks/section %}}

* 资料
由于 Zig 目前还处于快速迭代，因此最权威的资料无疑是官方的 [[https://ziglang.org/documentation/master/][Zig Language Reference]]，遇到语言的细节问题，基本都可以在这里找到答案。
其次是社区的一些高质量教程，例如：
- [[https://zig.guide/][Zig Guide]] :: 英文资料， [[https://github.com/Sobeston][Sobeston]] 用户编写
- [[https://gist.github.com/ityonemo/769532c2017ed9143f3571e5ac104e50][Zig in 30 minutes]] ::
- [[https://ziglang.cc/learning-zig/][学习 Zig]] :: 该系列教程最初由 Karl Seguin 编写，该教程行文流畅，讲述的脉络由浅入深，深入浅出，是入门 Zig 非常不错的选择
- [[https://course.ziglang.cc][Zig 语言圣经]] :: 一份内容全面、深入浅出介绍 Zig 的教程
- [[https://codeberg.org/ziglings/exercises/][ziglings/exercises]] :: Learn the Zig programming language by fixing tiny broken programs.
- [[https://cookbook.ziglang.cc/][Zig Cookbook]] :: A collection of simple Zig programs that demonstrate good practices to accomplish common programming tasks
- [[https://github.com/zigcc/awesome-zig][Awesome Zig]] :: A collection of some awesome public Zig programming language projects.

* 版本管理
推荐使用版本管理工具 [[/post/2023/10/14/zig-version-manager/][asdf]] 来安装 Zig，具体步骤：
#+begin_src bash
git clone https://github.com/asdf-vm/asdf.git ~/.asdf --branch v0.14.0
cat <<'EOF' >> $HOME/.bashrc
source "$HOME/.asdf/asdf.sh"
source "$HOME/.asdf/completions/asdf.bash"
EOF

asdf plugin-add zig https://github.com/zigcc/asdf-zig.git

# 安装最新版
asdf install zig latest
asdf global zig latest
zig version
#+end_src

{{% /blocks/section %}}

{{% blocks/section %}}

# This is another section
{.text-center}

{{% /blocks/section %}}
