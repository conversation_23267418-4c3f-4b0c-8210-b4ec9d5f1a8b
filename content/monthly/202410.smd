---
.title = "202410 | 向 Zig 软件基金会认捐 30 万美元",
.date = @date("2024-10-26T00:17:35+0800"),
.author = "ZigCC",
.layout = "post.shtml",
.draft = false,
---

* 重大事件
** 向 Zig 软件基金会认捐 30 万美元
Mitchell 在其最新的博客上宣布：我和我的妻子向 Zig 软件基金会 (ZSF) 捐赠了 300,000 美元。
#+begin_quote
两年内每年分期支付15万美元。第一期已经转账。
#+end_quote

我从 2019 年的某个时候开始关注 Zig 项目。 我在 2021 年公开分享了我对该项目的兴奋之情。 同年晚些时候，我开始使用 Zig，到 2022 年初，我开始撰写关于 Zig 的文章，并为编译器做出贡献。 2023 年，我公开分享了用 Zig 编写的终端项目 Ghostty。

如今，我大部分的编码时间都花在了 Zig 上。 我的家人喜欢支持我们相信的事业2。 作为其中的一部分，我们希望支持那些我们认为可以带来变革和影响的独立软件项目，这既是回馈给我如此之多的社区的一种方式，更重要的是，这也是彰显和鼓励为热爱而构建的文化的一种方式。 Zig 就是这样一个项目。
* 观点/教程
** [[https://mrcat.au/blog/zig_is_cool/][Zig is everything I want C to be]]
对 Zig 的特色进行了简单扼要的介绍，主要有：
1. UB 行为检测。
   - Zig 的指针不能是 null，需要用 optional 类型
   - C 里面的 =void*= 等价于 Zig 里面的 =?*anyopaque= 。 =void= 在 C 里面有两个意思，第一是『什么都没有』，第二是『类型不确定』，但 =void= 在 Zig 中只有第一个含义，因此用了 =anyopaque= 来表示类型擦除的指针（type-erased pointers）。
   - 数组越界检查
   - 整数溢出
2. Bitfield， =packed struct= 可以方便的用来进行协议解析，比如对于 32 位的 RISC-V 的指令，可以这么定义解析：
   #+begin_src zig
const IType = packed struct {
    opcode: u7,
    rd: u5,
    funct3: u3,
    rs1: u5,
    imm: i12, // For sign-extension
};

const encoded_instr: u32 = 0xFFF34293;
const instr: IType = @bitCast(encoded_instr);
   #+end_src
3. comptime，Zig 进行元编程的基础，类型是一等成员
4. 与 C 无缝交互， =zig cc= 是交叉编译的首选
** [[https://blog.erikwastaken.dev/posts/2024-10-27-building-3ds-homebrew-with-zig.html][Building Nintendo 3DS Homebrew with Zig]]
** [[https://kristoff.it/blog/critical-social-infrastructure/][Critical Social Infrastructure for Zig Communities | Loris Cro's Blog]]
对于一个试图共同学习如何制作大家都喜欢的软件的社区来说，能够分享想法并开展合作至关重要，但社交平台的不断起伏会导致连接中断，这对于一个从一开始就希望去中心化的社区来说是个大问题。

我有这种想法已经有一段时间了，但随着时间的推移，我们似乎越来越清楚地认识到，我们需要投资于能够长期保持可靠的交流形式，在这种交流形式中，变化是一种信号，表明社区正在发生转变（因此需要一种新的网络形态），而不是表明所选择的社交平台即将被收购/上市/加入人工智能大战。

开发者日志：迈向可靠社会基础设施的第一步
- https://ziglang.org/devlog/
- https://zine-ssg.io/log/
** [[https://ziglang.org/news/website-zine/][The Zig Website Has Been Re-engineered]]
Zig 官网已经用 [[https://zine-ssg.io/][Zine]] 重写！
** [[https://thenewstack.io/rust-vs-zig-in-reality-a-somewhat-friendly-debate/][Rust vs. Zig in Reality: A (Somewhat) Friendly Debate]]
** [[https://kevinlynagh.com/rust-zig/][Why I rewrote my Rust keyboard firmware in Zig: consistency, mastery, and fun]]
** [[https://teckrevie.blogspot.com/2024/09/why-zig-programmers-are-cashing-in-deep.html][Why Zig Programmers Are Cashing In: A Deep Dive into the Lucrative World of Zig]]
** 视频
*** [[https://www.youtube.com/watch?v=xOySJpQlmv4&feature=youtu.be][I made an operating system that self replicates doom on a network “from scratch”]]
*** [[https://www.youtube.com/watch?feature=shared&v=3fWx5BOiUiY][Rust vs Zig vs Go: Performance (Latency - Throughput - Saturation - Availability)]]
*** [[https://www.youtube.com/live/Kf7BIPUUfsc?t=764][Let's explore Vulkan API with Zig programming language from scratch]]
* 项目/工具
- [[https://github.com/laohanlinux/boltdb-zig][laohanlinux/boltdb-zig]] :: a zig implement kv database
- [[https://github.com/E-xyza/zigler][zigler]] :: Zig NIFs in Elixir
- [[https://github.com/gdonald/blackjack-zig][gdonald/blackjack-zig]] :: Console Blackjack written in Zig
- [[https://github.com/rabinnh/zig-vscode-linux][rabinnh/zig-vscode-linux]] :: Instructions on setting up VSCode to debug Zig on Linux
- [[https://github.com/lframosferreira/brainzuck][lframosferreira/brainzuck]] :: [[https://en.wikipedia.org/wiki/Brainfuck][Brainf*ck]] interpreter written in Zig 0.12.0! Have fun!
- [[https://github.com/BitlyTwiser/snek][BitlyTwiser/snek]] :: A simple CLI parser to build CLI applications in Zig
- [[https://github.com/zml/zml][zml/zml]] :: High performance AI inference stack. Built for production.
- [[https://github.com/BitlyTwiser/zdotenv][BitlyTwiser/zdotenv]] :: A port of Godotenv for Zig
- [[https://github.com/sbancuz/OpenMP-zig][sbancuz/OpenMP-zig]] ::  An implementation of the OpenMP directives for Zig
- [[https://github.com/tusharsadhwani/zigimports][tusharsadhwani/zigimports]] :: Automatically remove unused imports and globals from Zig files.
- [[https://github.com/Mario-SO/zigitor][Mario-SO/zigitor]] ::  Video editor 🎬 written in Zig ⚡ using raylib
- [[https://github.com/pwbh/ymlz][pwbh/ymlz]] ::  Small and convenient yaml parser for Zig
* [[https://github.com/ziglang/zig/pulls?page=1&q=+is%3Aclosed+is%3Apr+closed%3A2024-10-01..2024-11-01][Zig 语言更新]]