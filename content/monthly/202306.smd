---
.title = "202306 | Zig 要分叉了？",
.date = @date("2023-07-01T17:35:43+0800"),
.author = "ZigCC",
.layout = "post.shtml",
.draft = false,
---

* 重大事件
一个是这个：[[https://ziggit.dev/t/the-zig-subreddit-has-closed/679][The Zig subreddit has closed]]，现在 Ziggit 算是官方钦定的论坛了。

另一个是月底出来的大新闻：[[https://github.com/ziglang/zig/issues/16270][File for Divorce from LLVM · Issue #16270 · ziglang/zig]]

这个 issue 主要讨论的是把 LLVM 从 Zig 中彻底移除，动机和优势都列在里面了，这里不再赘述，这里重点说下影响：
1. 去掉 C++/Objc 的支持，
2. 支持的 target 会变少

从 issue 本身和 [[https://lobste.rs/s/svhzj9/divorce_from_llvm][Lobsters]]、[[https://news.ycombinator.com/item?id=36529456][HN]] 上的评论看，大家主要担忧的是对 C++ 的支持。由于非常多的基础软件都是构建在 C++ 之上的，如果没有了对 C++ 的支持，那么 Zig 作为工具链这一选择的可行性就大打折扣了，要知道 Zig 之前最主要的卖点就是这个，比如：[[https://kristoff.it/blog/maintain-it-with-zig/][Maintain it With Zig]]。

有人提议用 Zig 重写一个 C++ 前端不就好了？但这属于理论上可行，实际没有可操作性的，因为 C++ [[https://news.ycombinator.com/item?id=36532170][太复杂]]了。

其实 Zig 从 [[https://ziglang.org/download/0.10.0/release-notes.html][0.10.0]] 版本开始，就一直在着手 [[https://ziglang.org/download/0.10.0/release-notes.html#Self-Hosted-Compiler][Self-Hosted Compiler]] 的开发。看得出，Zig 团队一直在追求极致，从编译速度，到二进制大小（以下数字均来自 0.10.0 的 release note）：
- Wall Clock Time: 43 seconds to 40 seconds (7% faster)
- Peak RSS: 9.6 GiB to 2.8 GiB (3.5x less memory used)
- As a point of comparison, a stripped release build of Zig with LLVM is 169 MiB, while without LLVM (but with all the code generation backends you see here) it is 4.4 MiB.

这个 issue 在互联网上迅速引起了热烈讨论，当然少不了吃瓜群众，以至于 Andrewk 又追加了[[https://github.com/ziglang/zig/issues/16270#issuecomment-1615388680][一条评论]]：
#+begin_quote
I see a lot of speculation in this GitHub Issue from folks who are not involved in Zig in any way. I would respectfully ask you to please take such speculation elsewhere. This issue tracker is for focused technical discussion by those who are actually using Zig, today. The noise in this thread distracts from the valuable comments by users who are sharing their use cases for the relevant features of Zig.

..which, by the way, I'm one of. For example, my music player reboot branch depends on chromaprint which is, dun dun dun, C++ code.

I'm not going to simultaneously shoot myself and valuable community members in the face by yanking a load-bearing feature out from underneath us, without any kind of upgrade path. It's a bit unfortunate that the Internet has taken that narrative and run with it.

For example, one thing to explore, later - once all those boxes above are checked - is whether we can satisfy the C++ compilation use case, as well as the LLVM optimization use case, with the package manager. The results of this exploration will heavily impact the ultimate decision of whether to accept or reject this proposal.

Please, relax. Nothing is going to happen overnight, and nothing is more important than making sure our esteemed Zig users' needs are taken care of, one way or another. Whatever happens will happen in due time, with due respect for real world projects. This proposal is aspirational - something to look forward to and consider in the coming years.
#+end_quote

微信群里不少小伙伴也在担忧这个 issue 会不会导致 Zig 的 fork，甚至灭亡。其实看了上面的评论大家就应该放心了，Zig 团队知道用户的需要，不会搬起石头砸自己的脚。

我觉得通过这个事件更能坚定我投资 Zig 的信心了，一个追求极致的团队，不需要我们吃瓜群众瞎操心，有这个时间不如去看看 Zig 的各种 backend 进展，能不能给 fix 几个 regression？！

最后，即便 Zig 这个项目夭折了，我相信通过这个学习的过程也有助于提高我们对系统编程、编译器的理解，不是吗？
* 观点/教程
- [[https://kristoff.it/blog/note-about-zig-books/][A Note About Zig Books for the Zig Community]] ::  Loris Cro 的博客，由于现在 Zig 的关注度越来越高，一些出版社开始联系社区的人出一本 Zig 的书，Loris 这里阐述了与出版社合作的利弊，以及他也在写一本关于 Zig 的书 =Intro to Zig / systems programming= ，由于 Zig 还是不停的开发中，因为书中会尽量少的去涉及 stdlib 的内容。
- [[https://ziggit.dev/t/how-far-away-is-0-11-really/744][How far away is 0.11 really?]] :: 社区用户对 0.11 版本发布时间的疑问？
- [[https://devlog.hexops.com/2023/mach-ecosystem-c-libraries/][Mach: providing an ecosystem of C libraries using the Zig package manager]] :: 作者在文章讲述了利用 Zig 来打包 C 依赖的优势。
- [[https://ryleealanza.org/2023/06/21/The-Seamstress-Event-Loop-in-Zig.html][The Seamstress Event Loop In Zig]] ::
- [[https://notes.eatonphil.com/2023-06-19-metaprogramming-in-zig-and-parsing-css.html][Metaprogramming in Zig and parsing CSS]] ::
- [[https://en.liujiacai.net/2023/06/29/embed-git-commit-in-zig/][Embed git commit in Zig programs]] :: 把 git 的 commit id 嵌入项目中非常有助于问题排查
- [[https://avestura.dev/blog/problems-of-c-and-how-zig-addresses-them][Problems of C, and how Zig addresses them]] :: 不错的入门资料，主要内容：
  - Comptime over Textual Replacement Preprocessing
  - Memory Management, and Zig Allocators
  - Billion dollar mistake vs Zig Optionals
  - Pointer arithmetics vs Zig Slices
  - Explicit memory alignment
  - Arrays as values
  - Error handling
  - Everything is an expression
  - C has a more complex syntax to deal with
- [[https://richiejp.com/zig-cross-compile-ltp-ltx-linux][Minimal Linux VM cross compiled with Clang and Zig]] :: 一篇有意思的文章，作者的任务是跑 Linux kernel 的 test，为了能够方便、简单的跑不同的平台，作者尝试用 Zig 的交叉编译能力来解决这个大难题
- [[https://www.openmymind.net/Zig-Danling-Pointers/][Zig dangling pointers and segfaults]] ::
- [[http://ratfactor.com/zig/hard][I think Zig is hard...but worth it]] :: 安利 Zig 的文章，[[https://news.ycombinator.com/item?id=36149462][HN 讨论]]
  http://ratfactor.com/zig/zighard_700px.jpg
* 项目/工具
- [[https://github.com/pondzdev/duckdb-proxy/][pondzdev/duckdb]] :: 一个将 DuckDB 数据库通过 HTTP API 暴露出来的代理，主要是利用了 [[https://duckdb.org/docs/api/c/api.html][DuckDB C API]]，示例：
  #+begin_src bash
# open the database in readonly (DB must exist in this case)
$ ./duckdb-proxy --readonly db/mydatabase.duckdb

$ curl http://localhost:8012/api/1/exec \
	 -d '{"sql": "select version()"}'
{
  "cols": [
    "version()"
  ],
  "rows": [
    [
      "v0.8.1"
    ]
  ]
}
  #+end_src
- [[https://github.com/ryleelyman/seamstress][ryleelyman/seamstress]] :: Lua monome + OSC scripting environment
- [[https://programming-language-benchmarks.vercel.app/rust-vs-zig][Rust VS Zig benchmarks]] :: Which programming language or compiler is faster
- [[https://github.com/ziglang/shell-completions][ziglang/shell]] :: Shell completions for the Zig compiler.
- [[https://github.com/menduz/zig-steamworks][menduz/zig]] :: Steamwork bindings for Zig.
* [[https://github.com/ziglang/zig/pulls?page=1&q=+is%3Aclosed+is%3Apr+closed%3A2023-05-01..2023-06-01][Zig 语言更新]]
- [[https://github.com/ziglang/zig/pull/16207][WASI: Implement experimental threading support by Luukdegram · Pull Request #16207 · ziglang/zig]]