---
.title = "202404 | Zig 0.12.0 正式释出",
.date = @date("2024-04-18T21:59:49+0800"),
.author = "ZigCC",
.layout = "post.shtml",
.draft = false,
---

* 重大事件
千呼万唤的 0.12.0 版本终于 2024-04-20 正式释出了！这次版本历时 8 个月，有 268 位贡献者，一共进行了 3688 次提交！社区内的一些讨论：[[https://news.ycombinator.com/item?id=40096176][Hacker News]]、[[https://lobste.rs/s/fa4svu][Lobsters]]。
这是它的 [[https://ziglang.org/download/0.12.0/release-notes.html][Release notes]]。ZigCC 对这个文档进行了翻译、整理，方便大家阅读：
- [[https://course.ziglang.cc/update/upgrade-0.12.0][0.12.0 升级指南]]
- [[https://course.ziglang.cc/update/0.12.0-description][0.12.0 版本说明]]

并且还在 2024-04-27 举行了一次线上的 meetup 来庆祝这次发布，这是会议的总结：[[https://ziglang.cc/news/2024/04/27/release-party-review/][0.12.0 Release Party 回顾]]。

0.12.0 这个版本，对用户来说，最重大的变更就是构建系统的稳定了，这对于 Zig 生态的发展是十分关键的一步，试想一个项目用到的依赖之间版本不兼容，
这是十分痛苦的事情，毫无疑问这是阻碍 Zig 生态发生的绊脚石，没有之一。好在这一切都在 0.12 这个版本解决了，用户可以基于 Step
构成的有向无环图来编译自己的项目，不需要再折腾 CMake、Makefile、Vcpkg、Git submodule 等工具，所有的依赖使用 zon 来管理即可。
读者如果对 Zig 构建系统还不熟悉，可以参考：
- 官方文档：[[https://ziglang.org/learn/build-system/][Zig Build System]]
- Zig 升级： [[https://course.ziglang.cc/engineering/build-system][构建系统]]

期待一年后 Zig 的生态！
* 观点/教程
- [[https://github.com/zigcc/forum/issues/112][Zig 中任意精度整数用途与实现]] :: 由于 CPU 在访问内存时，一般都会有对齐的要求，对于这种非常规的数字，在内存中的地址会是怎样的呢？可以做一个简单的实验：

  #+begin_src zig
const std = @import("std");

const Foo = packed struct {
    a: u3,
    b: u2,
};

pub fn main() !void {
    const vs = [_]u3{ 1, 2, 3 };
    for (&vs) |*b| {
        std.debug.print("{p}-{b}\n", .{ b, b.* });
    }

    std.debug.print("U3 size: {d}\n", .{@sizeOf(u3)});
    std.debug.print("Foo size: {d}\n", .{@sizeOf(Foo)});

    const foos = [_]Foo{
        .{ .a = 1, .b = 3 },
    };

    std.debug.print("foo as bytes: {b}\n", .{std.mem.sliceAsBytes(&foos)});

    for (foos) |b| {
        std.debug.print("{any}-{any}\n", .{ &b.a, &b.b });
    }
}
  #+end_src
  输出：
  #+begin_src bash
u3@104d11a2c-1
u3@104d11a2d-10
u3@104d11a2e-11
U3 size: 1
Foo size: 1
foo as bytes: { 11001 }
u3@16b196367-u2@16b196367
  #+end_src

  通过前三个输出可以知道，每个 u3 实际占用一个字节，但当用在 packed 结构中，就会变成 3 个 bit。其中的 11001 就是字段 a b 混合后的值，且 a 是三位，b 是高两位。
- [[https://procmarco.com/blog/learnings-from-building-a-db-in-zig/][Learnings From Building a DB in Zig]] :: 作者分享了在一次 3 天的 Hackthon 中，使用 Zig 开发一个数据库的经历。
- [[https://zig.news/michalsieron/buildzigzon-dependency-hashes-47kj][build.zig.zon dependency hashes]] :: 讲解了 zon 中依赖的 hash 是怎么计算出来的
- [[https://zig.news/liyu1981/play-with-new-comptime-var-rule-of-zig-0120-333k][play with new comptime var rule of zig 0.12.0]] ::
- [[https://zig.news/inspectorboat/to-simd-and-beyond-optimizing-a-simple-comparison-routine-1jkf][To SIMD and beyond: Optimizing a simple comparison routine]] :: 作者在这里循序渐进的介绍了几种数字比较的技巧，从基本的方案，到 Vector，到最后利用 bit 的特点，来逐步优化，并用 godbolt 查看生成的汇编代码，是一篇不错的文章。
- [[https://www.reddit.com/r/Zig/comments/1cc1x2v/documentation_takes_another_step_backwards/][Documentation takes another step backwards : r/Zig]] :: 一个 Reddit 用户对文档的抱怨
* 项目/工具
- [[https://github.com/rofrol/zig-companies][rofrol/zig-companies]] :: A list of companies using Zig in production.
- [[https://github.com/akarpovskii/tuile][akarpovskii/tuile]] :: A Text UI library for Zig
- [[https://codeberg.org/mntnmntn/zenith][mntnmntn/zenith]] :: A very minimal text editor in Zig，支持 0.12.0 版本
- [[https://github.com/chung-leong/zigar][chung-leong/zigar]] :: Enable the use of Zig code in JavaScript project
- [[https://github.com/jnordwick/zig-string][jnordwick/zig-string]] :: Zig string library that includes small string optimization on the stack
- [[https://github.com/FalsePattern/ZigBrains][FalsePattern/ZigBrains]] :: Yet another zig language plugin for intellij

* [[https://github.com/ziglang/zig/pulls?page=1&q=+is%3Aclosed+is%3Apr+closed%3A2024-04-01..2024-05-01][Zig 语言更新]]