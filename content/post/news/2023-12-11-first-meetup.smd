---
.title = "Zig 语言中文社区第一次线上会议",
.date = @date("2023-12-11T08:25:00+0800"),
.author = "ZigCC",
.layout = "post.shtml",
.draft = false,
---

#+TAGS[]: community

2023 年 12 月 9 日，Zig 中文社区第一次线上会议隆重召开。共有 8 位 Zig 爱好者参加，分布在北上杭成、美国等不同地方。

{{< figure src="/images/first-online-meeting.webp" caption="会议参会人员">}}

和当年的从仙童半导体出逃的人数一样，不多不少。😄
{{< figure src="/images/fair-children.webp" caption="硅谷八叛徒">}}
会议伊始，成员首先进行了个人简介，便于后续开展相应工作。随后，社区成员围绕 Zig 语言的普及进行了交流讨论。

在交流讨论环节，大家就 Zig 语言的普及面临的挑战和机遇进行了深入的探讨。其中，大家认为 Zig 语言的普及面临的主要挑战包括：

- Zig 语言是一个新兴的语言，知名度还不够高。
- Zig 语言的生态还不够完善，缺乏成熟的库和工具。

与此同时，大家也认为 Zig 语言的普及也具有一定的机遇，包括：

- Zig 语言具有很强的性能、安全性和易用性，具有一定的竞争力。
- Zig 语言的设计理念与 C 语言类似，对于 C 语言开发者来说具有较高的学习成本。

因此，第一阶段，我们打算推出一系列教程来帮助大家学习 Zig，目前主要有以下几个：

| 项目            | 参与人员        | 目标                                          | 仓库                          |
|----------------+---------------+----------------------------------------------+------------------------------|
| Zig 入门教程     | 金中甲         | 让没有编程背景的人可以有体系的学习 Zig             | [[https://github.com/learnzig/learnzig][learnzig/learnzig]]            |
| Zig 教学视频     | Onion、Lambert | 同上，素材取自 [[https://zigcc.github.io/learning-zig/][Learning Zig 中文翻译]]             |                              |
| Zig cookbook   | 夜白、冯文轩    | 演示如何用 Zig 做某个功能                       | [[https://github.com/zigcc/zig-cookbook][zigcc/zig-cookbook]]           |
| Zig 构建系统教程 | 贺鹏、陈瑞      | 体验 Zig 编译系统的能力与优势、与其他构建系统的对比 | [[https://zigcc.github.io/post/][zigcc 网站系列文章]]             |
| Zig 写 OS 教程  | 柠檬、西瓜      | 体现 Zig low level 的优势                      | [[https://github.com/zigcc/how-to-write-os-in-zig][zigcc/how-to-write-os-in-zig]] |
| Zig 惯用法      | 全体           | 收集 Zig 编程技巧                              | [[https://github.com/zigcc/zig-idioms][zigcc/zig-idioms]]             |

我们希望通过这些努力，提高 Zig 语言的知名度，完善 Zig 语言的生态，促进 Zig 语言的交流和学习。
* 结论
Zig 中文社区第一次线上会议的召开，标志着 Zig 社区正式启航。如果读者对共建社区感兴趣，欢迎与我们联系。

- 邮箱：<EMAIL>