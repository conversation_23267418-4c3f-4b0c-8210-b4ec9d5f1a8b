---
.title = "欢迎 Zig 爱好者向本网站供稿",
.author = "刘家财",
.date = @date("2023-09-05T16:13:13"),
.layout = "post.shtml",
.draft = false,
---

欢迎社区用户向 ZigCC 供稿（关于 Zig 的任何话题），方便与社区更多人分享。文章会发布在：

- [ZigCC 网站](https://ziglang.cc)
- [ZigCC 公众号](https://github.com/zigcc/.github/raw/main/zig_mp.png)

# 供稿方式

1. Fork 仓库 https://github.com/zigcc/zigcc.github.io
2. 在 `content/post` 内添加自己的文章（md 或 org 格式均可），文件命名为： `${YYYY}-${MM}-${DD}-${SLUG}.md`
3. 文件开始需要包含一些描述信息，例如[本文件](https://github.com/zigcc/zigcc.github.io/tree/main/content/post/2023-09-05-hello-world.md)中的：

```plain
---
title: 欢迎 Zig 爱好者向本网站供稿
author: 刘家财
date: '2023-09-05T16:13:13+0800'
---
```

## 本地预览

在写完文章后，可以使用 [Hugo](https://gohugo.io/) 进行本地预览，只需在项目根目录执行 `hugo server`，这会启动一个 HTTP 服务，默认的访问地址是： http://localhost:1313/
