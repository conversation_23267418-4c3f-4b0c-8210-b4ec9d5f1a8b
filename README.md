# Zig 语言中文社区 (zig-zine 版本)

这是 [Zig 语言中文社区](https://ziglang.cc) 网站的 zig-zine 版本。

## 关于转换

本项目已从 Hugo 静态站点生成器转换为 [zig-zine](https://zine-ssg.io/)。主要变化包括：

- **配置文件**: 从 `config.toml` 转换为 `zine.ziggy`
- **内容格式**: 从 Markdown (`.md`) 转换为 SuperMD (`.smd`)
- **布局系统**: 从 Hugo 模板转换为 SuperHTML (`.shtml`)
- **构建系统**: 使用 Zig 构建系统和 zig-zine

## 项目结构

```
├── zine.ziggy          # 主配置文件
├── build.zig           # Zig 构建脚本
├── build.zig.zon       # Zig 包管理文件
├── content/            # 内容文件 (SuperMD)
│   ├── index.smd       # 首页
│   ├── learn/          # 学习教程
│   ├── post/           # 博客文章
│   ├── monthly/        # 月刊
│   ├── community.smd   # 社区页面
│   └── contributing.smd # 贡献指南
├── layouts/            # 布局模板 (SuperHTML)
│   ├── base.shtml      # 基础布局
│   ├── home.shtml      # 首页布局
│   ├── post.shtml      # 文章布局
│   ├── blog.shtml      # 博客列表布局
│   ├── section.shtml   # 章节布局
│   ├── monthly.shtml   # 月刊布局
│   └── page.shtml      # 普通页面布局
└── assets/             # 静态资源
    ├── favicons/       # 网站图标
    └── images/         # 图片资源
```

## 开发环境设置

### 前提条件

1. 安装 [Zig](https://ziglang.org/download/) (推荐最新版本)
2. 安装 [zig-zine](https://zine-ssg.io/quickstart/)

### 本地开发

1. 克隆仓库：
```bash
git clone https://github.com/zigcc/zigcc.github.io.git
cd zigcc.github.io
```

2. 启动开发服务器：
```bash
zine
```

3. 在浏览器中访问 `http://localhost:1990`

### 构建网站

```bash
zine build
```

构建的网站将输出到 `zig-out/` 目录。

## 内容编写

### 文章格式

所有内容文件使用 SuperMD 格式 (`.smd`)，frontmatter 使用 Ziggy 语法：

```smd
---
.title = "文章标题",
.author = "作者名",
.date = @date("2023-09-05T16:13:13"),
.layout = "post.shtml",
.draft = false,
---

文章内容使用 SuperMD 格式...
```

### 添加新文章

1. 在 `content/post/` 目录下创建新的 `.smd` 文件
2. 文件命名格式：`YYYY-MM-DD-slug.smd`
3. 填写正确的 frontmatter
4. 使用 SuperMD 语法编写内容

### 添加新的学习教程

1. 在 `content/learn/` 目录下创建新的 `.smd` 文件
2. 使用 `section.shtml` 布局
3. 在导航中添加相应链接

## 布局系统

布局使用 SuperHTML 格式，支持：

- 模板继承 (`<extend template="base.shtml">`)
- 条件渲染 (`<if :condition="...">`)
- 循环 (`<loop :each="...">`)
- 动态属性 (`:text`, `:href`, `:html`)

## 部署

网站可以部署到任何静态托管服务，如：

- GitHub Pages
- Cloudflare Pages
- Netlify
- Vercel

构建命令：`zine build`
输出目录：`zig-out/`

## 贡献

欢迎社区成员贡献内容和改进！请参考 [贡献指南](content/contributing.smd)。

## 许可证

如果没有特殊声明，本网站内容均采用 [CC BY-NC-ND 4.0](https://creativecommons.org/licenses/by-nc-nd/4.0/) 协议。

## 社区链接

- [GitHub Discussion](https://github.com/orgs/zigcc/discussions)
- [Discord](https://discord.gg/UraRxD6WXD)
- [Telegram](https://t.me/ZigChinese)
- [邮件](mailto:<EMAIL>)
