#!/usr/bin/env python3
"""
<PERSON>ript to clean up old Hugo files after zig-zine conversion.
This script will move old files to a backup directory instead of deleting them.
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def create_backup_dir():
    """Create a backup directory for old Hugo files."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"hugo_backup_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir

def move_to_backup(file_path, backup_dir):
    """Move a file to the backup directory."""
    if os.path.exists(file_path):
        dest_path = os.path.join(backup_dir, os.path.basename(file_path))
        shutil.move(file_path, dest_path)
        print(f"Moved {file_path} -> {dest_path}")
        return True
    return False

def main():
    """Main cleanup function."""
    print("Hugo to Zig-Zine Cleanup Script")
    print("=" * 40)
    
    # Create backup directory
    backup_dir = create_backup_dir()
    print(f"Created backup directory: {backup_dir}")
    
    # Files to move to backup
    hugo_files = [
        "config.toml",
        "go.mod", 
        "go.sum",
        ".gitmodules",
        "Makefile",
        ".lintmdrc",
        ".prettierignore",
        "README.org",
    ]
    
    # Directories to move to backup
    hugo_dirs = [
        "archetypes",
        "static",
        "examples",
        "layouts/partials",  # Keep our new layouts, backup old partials
    ]
    
    moved_count = 0
    
    # Move files
    print("\nMoving Hugo configuration files...")
    for file_path in hugo_files:
        if move_to_backup(file_path, backup_dir):
            moved_count += 1
    
    # Move directories
    print("\nMoving Hugo directories...")
    for dir_path in hugo_dirs:
        if os.path.exists(dir_path):
            dest_path = os.path.join(backup_dir, os.path.basename(dir_path))
            shutil.move(dir_path, dest_path)
            print(f"Moved {dir_path} -> {dest_path}")
            moved_count += 1
    
    # Move original content files (keep .smd, backup .md and .org)
    print("\nMoving original content files...")
    content_backup_dir = os.path.join(backup_dir, "content_originals")
    os.makedirs(content_backup_dir, exist_ok=True)
    
    content_dir = Path("content")
    if content_dir.exists():
        for file_path in content_dir.glob("**/*.md"):
            # Only move if corresponding .smd exists
            smd_path = file_path.with_suffix('.smd')
            if smd_path.exists():
                rel_path = file_path.relative_to(content_dir)
                dest_dir = os.path.join(content_backup_dir, os.path.dirname(rel_path))
                os.makedirs(dest_dir, exist_ok=True)
                dest_path = os.path.join(dest_dir, file_path.name)
                shutil.move(str(file_path), dest_path)
                print(f"Moved {file_path} -> {dest_path}")
                moved_count += 1
        
        for file_path in content_dir.glob("**/*.org"):
            # Only move if corresponding .smd exists
            smd_path = file_path.with_suffix('.smd')
            if smd_path.exists():
                rel_path = file_path.relative_to(content_dir)
                dest_dir = os.path.join(content_backup_dir, os.path.dirname(rel_path))
                os.makedirs(dest_dir, exist_ok=True)
                dest_path = os.path.join(dest_dir, file_path.name)
                shutil.move(str(file_path), dest_path)
                print(f"Moved {file_path} -> {dest_path}")
                moved_count += 1
    
    # Create a summary file in the backup directory
    summary_path = os.path.join(backup_dir, "BACKUP_SUMMARY.txt")
    with open(summary_path, 'w') as f:
        f.write(f"Hugo to Zig-Zine Conversion Backup\n")
        f.write(f"Created: {datetime.now().isoformat()}\n")
        f.write(f"Files moved: {moved_count}\n\n")
        f.write("This directory contains the original Hugo files that were\n")
        f.write("replaced during the zig-zine conversion. These files are\n")
        f.write("kept for reference and can be safely deleted once the\n")
        f.write("zig-zine conversion is confirmed to be working correctly.\n")
    
    print(f"\nCleanup complete!")
    print(f"Moved {moved_count} files/directories to {backup_dir}")
    print(f"Created summary: {summary_path}")
    print("\nYour project is now ready for zig-zine!")
    print("\nNext steps:")
    print("1. Install zig-zine: https://zine-ssg.io/quickstart/")
    print("2. Run 'zine' to start development server")
    print("3. Run 'zine build' to build the site")
    print(f"4. If everything works, you can delete {backup_dir}")

if __name__ == "__main__":
    main()
