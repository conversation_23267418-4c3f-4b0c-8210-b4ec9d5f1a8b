<extend template="base.shtml">
<div class="container my-5">
    <div class="row">
        <div class="col-lg-9">
            <h1 :text="$page.title"></h1>
            <div :html="$page.content()"></div>
            
            <if :condition="$page.subpages().len() > 0">
                <div class="mt-5">
                    <h2>章节列表</h2>
                    <div class="list-group">
                        <loop :each="$page.subpages()">
                            <a :href="$loop.it.link()" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1" :text="$loop.it.title"></h5>
                                    <small :text="$loop.it.date.format('2006-01-02')"></small>
                                </div>
                                <if :condition="$loop.it.author">
                                    <small>作者：<span :text="$loop.it.author"></span></small>
                                </if>
                            </a>
                        </loop>
                    </div>
                </div>
            </if>
        </div>
        
        <div class="col-lg-3">
            <div class="card">
                <div class="card-header">
                    <h5>导航</h5>
                </div>
                <div class="card-body">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/learn">学习首页</a>
                        <a class="nav-link" href="/learn/installing-zig">安装 Zig</a>
                        <a class="nav-link" href="/learn/language-overview-part1">语言概览（上）</a>
                        <a class="nav-link" href="/learn/language-overview-part2">语言概览（下）</a>
                        <a class="nav-link" href="/learn/pointers">指针</a>
                        <a class="nav-link" href="/learn/stack-memory">栈内存</a>
                        <a class="nav-link" href="/learn/heap-memory-and-allocator">堆内存和分配器</a>
                        <a class="nav-link" href="/learn/generics">泛型</a>
                        <a class="nav-link" href="/learn/coding-in-zig">在 Zig 中编程</a>
                        <a class="nav-link" href="/learn/style-guide">代码风格指南</a>
                        <a class="nav-link" href="/learn/conclusion">结语</a>
                    </nav>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5>相关资源</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><a href="https://ziglang.org/documentation/master/" target="_blank">官方文档</a></li>
                        <li><a href="https://course.ziglang.cc/" target="_blank">Zig 语言圣经</a></li>
                        <li><a href="https://cookbook.ziglang.cc/" target="_blank">Zig Cookbook</a></li>
                        <li><a href="https://zig.guide/" target="_blank">Zig Guide</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
</extend>
