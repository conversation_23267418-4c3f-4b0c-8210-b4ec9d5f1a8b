#!/usr/bin/env python3
"""
Validation script for the Hugo to zig-zine conversion.
"""

import os
import sys
from pathlib import Path

def check_file_exists(path, description):
    """Check if a file exists and report."""
    if os.path.exists(path):
        print(f"✓ {description}: {path}")
        return True
    else:
        print(f"✗ {description}: {path} (missing)")
        return False

def check_directory_structure():
    """Check if the zig-zine directory structure is correct."""
    print("=== Directory Structure ===")
    
    required_files = [
        ("zine.ziggy", "Main configuration file"),
        ("build.zig", "Build script"),
        ("build.zig.zon", "Package manifest"),
        ("content/index.smd", "Home page"),
        ("layouts/base.shtml", "Base layout"),
        ("layouts/home.shtml", "Home layout"),
        ("layouts/post.shtml", "Post layout"),
        ("assets/favicons/favicon.ico", "Favicon"),
    ]
    
    all_good = True
    for file_path, description in required_files:
        if not check_file_exists(file_path, description):
            all_good = False
    
    return all_good

def check_content_conversion():
    """Check if content files were converted properly."""
    print("\n=== Content Conversion ===")
    
    content_dir = Path("content")
    if not content_dir.exists():
        print("✗ Content directory missing")
        return False
    
    # Count converted files
    smd_files = list(content_dir.glob("**/*.smd"))
    md_files = list(content_dir.glob("**/*.md"))
    org_files = list(content_dir.glob("**/*.org"))
    
    print(f"✓ Found {len(smd_files)} .smd files (converted)")
    print(f"  Found {len(md_files)} .md files (original)")
    print(f"  Found {len(org_files)} .org files (original)")
    
    # Check specific important files
    important_files = [
        "content/index.smd",
        "content/learn/index.smd",
        "content/post/index.smd",
        "content/monthly/index.smd",
        "content/community.smd",
        "content/contributing.smd",
    ]
    
    all_good = True
    for file_path in important_files:
        if not check_file_exists(file_path, f"Important content file"):
            all_good = False
    
    return all_good

def check_frontmatter_format():
    """Check if frontmatter was converted to Ziggy format."""
    print("\n=== Frontmatter Format ===")
    
    sample_files = [
        "content/index.smd",
        "content/post/2023-09-05-hello-world.smd",
    ]
    
    all_good = True
    for file_path in sample_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.startswith('---') and '.title =' in content and '@date(' in content:
                    print(f"✓ {file_path}: Ziggy frontmatter format")
                else:
                    print(f"✗ {file_path}: Invalid frontmatter format")
                    all_good = False
        else:
            print(f"✗ {file_path}: File missing")
            all_good = False
    
    return all_good

def check_layout_files():
    """Check if layout files are in SuperHTML format."""
    print("\n=== Layout Files ===")
    
    layout_files = [
        "layouts/base.shtml",
        "layouts/home.shtml",
        "layouts/post.shtml",
        "layouts/blog.shtml",
        "layouts/section.shtml",
        "layouts/monthly.shtml",
        "layouts/page.shtml",
    ]
    
    all_good = True
    for file_path in layout_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if '<extend template=' in content or '<!DOCTYPE html>' in content:
                    print(f"✓ {file_path}: SuperHTML format")
                else:
                    print(f"? {file_path}: May need SuperHTML syntax review")
        else:
            print(f"✗ {file_path}: Missing")
            all_good = False
    
    return all_good

def check_assets():
    """Check if assets were copied correctly."""
    print("\n=== Assets ===")
    
    assets_dir = Path("assets")
    if not assets_dir.exists():
        print("✗ Assets directory missing")
        return False
    
    # Check for important asset directories
    asset_dirs = ["favicons", "images"]
    all_good = True
    
    for dir_name in asset_dirs:
        dir_path = assets_dir / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.glob("*")))
            print(f"✓ {dir_path}: {file_count} files")
        else:
            print(f"✗ {dir_path}: Missing")
            all_good = False
    
    return all_good

def main():
    """Main validation function."""
    print("Zig-Zine Conversion Validation")
    print("=" * 40)
    
    checks = [
        check_directory_structure,
        check_content_conversion,
        check_frontmatter_format,
        check_layout_files,
        check_assets,
    ]
    
    all_passed = True
    for check in checks:
        if not check():
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All validation checks passed!")
        print("\nNext steps:")
        print("1. Install zig-zine: https://zine-ssg.io/quickstart/")
        print("2. Run 'zine' to start development server")
        print("3. Run 'zine build' to build the site")
    else:
        print("❌ Some validation checks failed.")
        print("Please review the issues above before proceeding.")
        sys.exit(1)

if __name__ == "__main__":
    main()
